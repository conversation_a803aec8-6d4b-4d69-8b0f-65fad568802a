CMP4272 Data Structures and AlgorithmsLecture 5 : Graphs

Data Structures Revisited 

Facebook Friends Graph  

What is a graph?Graphs represent the relationships among data items A graph G consists of V - a set V of nodes (vertices) E- a set E of edges: each edge connects two nodes   Each node represents an item Each edge represents the relationship between two itemsAVertexG = (V,E)V = ( A )E = {  }BCDEV = ( A, B, C, D, E )EdgerelationshipE = { (A,B)}E = { (A,B), (A,C),(B,C), (B,D), (B,E), (C,E) }A and B (A,B)

What is a Graph formally?  A graph G is defined as G=(V,E): V(G): a finite, nonempty set of vertices E(G): a set of edges (pairs of vertices) If two graphs have the same nodes and the same links between nodes, then they are the same graph. The position of nodes does not matter. Connected  nodes are called  Adjacent when linked  by an Edge. If all nodes  are adjacent the graph  is complete. Adjacency  can be directional.  

Facebook Friends Graph  

Graph TerminologyABCDETwo vertices u and v are said to be adjacent if there exists an edge  between the two. ABCWhich nodes are adjacent to vertex A ?(A,B) , (A,C)How about vertex A and D ?There does not exist an edge between these two, these are not adjacentIs there a way to reach from vertex A to vertex D ?DAPath : Sequence of vertices connected by edges  Path A, B, D  connected by edges: (A,B) , (B,D)Are there any other paths between A and D ?Many paths, some of these areA -> D  : A,B,D     A,C,B,D     A,B,C,E,B,D

Path , CycleABCDESimple path : If every vertex appears at most once,  the path is simpleMany paths, but how about A,B,C,E,B,D?A -> D  : A,B,D     A,C,B,D     A,B,C,E,B,DTraverse these paths :  A, B, E, C, A A, B, C, A B, C, E, BAny pattern you see in these paths ?These paths begin and end at the same vertex.CycleABCA,B,C,E,B,D : Here a vertex (B) appears more than once in the path  While in other paths a vertex appears at most once.How about A, B, E, C, B, A ?A simple cycle is a cycle if every node appears at most once,  except for the first and the last nodesA loop (self-loop) is an edge that connects a vertex to itself

Trees vs. Graphs  The following relations don’t define tree structures: not a clear hierarchical relation anymore. A person is friends with another person. A Twitter user follows another user. A city is connected by a road to another city. A device is connected by a network cable to another device. A business buys from another business.  Each of these examples has “nodes” and “links”, but they are not trees; we call them graphs or networks. 

Graphs  These example all define graph structures, but not tree structures: There is no “root node”, and it isn’t necessary to choose one. They may be “symmetric” - if two devices are connected by a network cable, neither is really the “parent” or the “child”. They may allow “cycles” - Alice is friends with Bob, Bob is friends with Charles, and Charles is friends with Alice.﻿ They may allow “loops” - the webpage links to itself.﻿ 

Graphs: Roads  

Types of Graphs – Connected , DisconnectedThis is a connected graph because there exists path between every pair of nodes This is a disconnected graph because there does not exist path between some pair of nodes, says, v1 and v7G1G2each pair of distinct nodes has an edgecomplete

Types of GraphsHoustonChicago100020003500New Yorkeach edge is assigned a weightWeighed GraphDirected edgeHoustonChicago100020003500New Yorkevery edges is a directed edgeDirected Graph

Graph Data Structures  There are 3 components which define a graph data structure: Edge lists Adjacency lists Adjacency matrices  If the graph is weighted, then a Weight matrices are also required.

Graph Data Structures  A graph data structure should support the following operations: Create a new node Create a new edge between two nodes Check whether two nodes share an edge Remove an edge Remove a node and all its edges Iterate over all nodes Iterate over all edges Iterate over a node’s neighbours

Easy and intuitive representation Adding node/edge is easyEdge List Representation#6,”node6”, [“node1”, ”node6”]Check all neighbours of node1 ? Search

Edge List  A graph has nodes and edges — so the simplest graph data structure just has a list of nodes and a list of edges. Each item in edges is one edge, represented as the pair of nodes the edge is “from” and “to”. If the nodes are objects, then they have value and edges fields to hold the node’s value and its list of edges. 

Edge List Operations  The edge list data structure makes it easy to represent the data (intuitive). Simple operations like adding a node or an edge are straightforward, but finding a node’s neighbours, checking if an edge exists or removing an edge will require searching through the entire edge list.﻿ 

Adjacency List  If we want easy access to a node’s neighbours, we could simply store them with the node. This is the adjacency list data structure: If the nodes are objects, then they have value and neighbours fields to hold the node’s value and its list of neighbours 

Adjacency List Search for a nodes’ neighbours Ex : node1

Adjacency List Operations  An adjacency list is better than an edge list in almost every way.﻿ We can find a node’s neighbours via the node itself. So to check or remove an edge we only need to search through a node’s neighbours, rather than all of the graph’s edges. 

Adjacency Matrix  A matrix is a 2-dimensional array containing numbers or objects. An adjacency matrix has Boolean entries where 1 represents an edge between the column and row node and 0 indicates no edge.

Adjacency Matrix Operations  Adding, removing or checking an edge is very efficient.  The grid requires memory to store not just the edges, but also “non-edges”. This is especially bad if most nodes only have a few edges — then the grid is mostly 0s.  Adjacency matrices are therefore normally only used to check edges very efficiently.

Adjacency list for directed graphAdjacency list for weighted undirected graph

Adjacency matrix for directed graphAdjacency matrix for weighted undirected graph

To traverse a tree, we use tree traversal algorithms like pre-order, in-order, and post-order to visit all the nodes in a tree  Similarly, graph traversal algorithm tries to visit all the nodes it can reach. Graph Traversal Algorithm

Depth-first-search (DFS) After visit node v, DFS strategy proceeds along a path from v as deeply into the graph as possible before backing up Breadth-first-search (BFS) After visit node v, BFS strategy visits every node adjacent to v before visiting any other nodesBasic Traversal Algorithms

DFS ExampleStart from v3 v1v4v3v5v2Gv31v22v13v44v55xxxxx

DFS AlgorithmAlgorithm DFS(v) s.createStack(); s.push(v); mark v as visited; while (!s.isEmpty()) {   let x be the node on the top of the stack s;   if (no unvisited nodes are adjacent to x)     s.pop(); // blacktrack   else {     select an unvisited node u adjacent to x;     s.push(u);     mark u as visited;   } }

DFS Example –  Uses Stackv1v4v3v5v2Gx

Breadth First Search AlgorithmFrom a given node v, it first visits itself. Then, it visits every node adjacent to v before visiting any other nodes. 1. Visit v 2. Visit all v’s neigbours 3. Visit all v’s neighbours’ neighbours … BFS is based on a queue.

Breadth First Search AlgorithmAlgorithm BFS(v) q.createQueue(); q.enqueue(v); mark v as visited; while(!q.isEmpty()) {   w = q.dequeue();   for (each unvisited node u adjacent to w) {     q.enqueue(u);     mark u as visited;   } }

Start from v5v51xBFS Example

Exercises: Graph Data Structures  

Exercises: Graph Data Structures  

Exercises: Create Adjacency List:  

Exercises: Graph Data Structures  

Exercises: Graph Data Structures  

Exercises:

